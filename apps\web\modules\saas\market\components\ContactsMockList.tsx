"use client";
import React, { useState} from "react";
import { MOCK_FUNDS } from "../lib/TestData";
import ContextCard from "./ContextCard";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import {
	SquarePen,
	RotateCcw,
	Save,
} from "lucide-react";
import { cn } from "@ui/lib";
import { AILogo } from "@saas/market/components/AILogo";


// 这里等着传入的参数是组织slug和详情页的basePath 需要投资人信息做弹出页面信息
export default function ContactsMockList({
	organizationSlug,
	detailBasePath,
}: {
	organizationSlug: string;
	detailBasePath: string;
}) {
	const contacts = MOCK_FUNDS; // 联系人列表
	const [search, setSearch] = useState("");
	const [activeFilter, setActiveFilter] = useState("all");
	const [isEditing, setIsEditing] = useState(false);

	// 输入框状态管理
	const [companyCode, setCompanyCode] = useState("");
	const [benchmarkCodes, setBenchmarkCodes] = useState("");

	// 引用参数 方便后续不同组织使用
	console.log(organizationSlug, detailBasePath);
	// 搜索过滤（不区分大小写，支持全名模糊搜索，空关键字显示全部）
	const filteredContacts = !search.trim()
		? contacts
		: contacts.filter(c => {
				const keyword = search.trim().toLowerCase();
				return (
					c.name.toLowerCase().includes(keyword) ||
					c.code.toLowerCase().includes(keyword) ||
					c.manager.toLowerCase().includes(keyword) ||
					c.tag.toLowerCase().includes(keyword)
				);
			});

	return (
		<>
			<AILogo />
			{/* 搜索框区域：两个输入框+编辑和刷新按钮 */}
			<div className="flex items-center gap-3 w-full mb-0">
				{/* 公司代码输入框（较小） */}
				<div className="relative w-100">
					<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap pointer-events-none">
						输入公司股票代码：
					</span>
					<Input
						type="text"
						placeholder=""
						value={companyCode || ""}
						onChange={(e) => setCompanyCode(e.target.value)}
						readOnly={!isEditing}
						className={cn(
							"h-11 pl-32 pr-4 py-4 rounded-md shadow border text-md focus:outline-none",
							!isEditing
								? "bg-gray-50 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-gray-300 cursor-default focus:ring-0"
								: "bg-white dark:bg-zinc-900 border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary",
						)}
					/>
				</div>
				{/* 对标公司代码输入框（较大） */}
				<div className="relative w-200">
					<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap pointer-events-none">
						输入对标公司股票代码：
					</span>
					<Input
						type="text"
						placeholder=""
						value={benchmarkCodes || ""}
						onChange={(e) => setBenchmarkCodes(e.target.value)}
						readOnly={!isEditing}
						className={cn(
							"h-11 pl-40 pr-4 py-4 rounded-md shadow border text-md focus:outline-none",
							!isEditing
								? "bg-gray-50 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-gray-300 cursor-default focus:ring-0"
								: "bg-white dark:bg-zinc-900 border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary",
						)}
					/>
				</div>
				{/* 编辑和刷新按钮 */}
				<Button
					variant="ghost"
					className="h-11 w-11 p-0 flex items-center justify-center shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent"
					onClick={() => setIsEditing((v) => !v)}
					title={isEditing ? "保存" : "编辑"}
				>
					{isEditing ? (
						<Save className="w-6 h-6 text-gray-600 dark:text-gray-300" />
					) : (
						<SquarePen className="w-6 h-6 text-gray-600 dark:text-gray-300" />
					)}
				</Button>
				<Button
					variant="ghost"
					className="h-11 w-11 p-0 flex items-center justify-center shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent"
					title="刷新"
				>
					<RotateCcw className="w-6 h-6 text-gray-600 dark:text-gray-300" />
				</Button>
			</div>

			{/* 筛选按钮 */}
			<div className="flex items-center w-fit mt-0 ml-1 text-sm text-black border border-gray-200 rounded-lg px-4">
				<Button
					variant="ghost"
					onClick={() => setActiveFilter("all")}
					className={cn(
						"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
						activeFilter === "all"
							? "text-black dark:text-white"
							: "text-gray-600 font-normal",
					)}
				>
					全部
				</Button>
				<span className="mx-2 select-none text-gray-400">|</span>
				<Button
					variant="ghost"
					onClick={() => setActiveFilter("starred")}
					className={cn(
						"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
						activeFilter === "starred"
							? "text-black dark:text-white"
							: "text-gray-600 font-normal",
					)}
				>
					已收藏
				</Button>
				<span className="mx-2 select-none text-gray-400">|</span>
				<Button
					variant="ghost"
					onClick={() => setActiveFilter("unstarred")}
					className={cn(
						"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
						activeFilter === "unstarred"
							? "text-black dark:text-white"
							: "text-gray-600 font-normal",
					)}
				>
					未收藏
				</Button>
			</div>

			{/* 搜索结果为空 显示找不到该用户 */}
			{filteredContacts.length === 0 && search.trim() !== "" ? (
				<div className=" flex flex-col items-center justify-center h-80 text-gray-400 dark:text-gray-300 text-lg">
					No results found for "{search}"
				</div>
			) : (
				<ContextCard filtered={filteredContacts} />
			)}
		</>
	);
}